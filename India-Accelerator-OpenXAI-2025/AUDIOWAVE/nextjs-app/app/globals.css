@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

/* Audio visualization styles */
.audio-visualizer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.audio-bar {
  width: 3px;
  background: linear-gradient(to top, #8b5cf6, #06b6d4);
  border-radius: 2px;
  animation: audioWave 1s ease-in-out infinite alternate;
}

@keyframes audioWave {
  0% { height: 10px; }
  100% { height: 30px; }
} 