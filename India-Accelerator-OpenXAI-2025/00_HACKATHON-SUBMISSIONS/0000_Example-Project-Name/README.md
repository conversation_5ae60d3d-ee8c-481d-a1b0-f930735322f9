# OpenxAI Global AI Accelerator Example Submission

## Overview

Add your Next.js / Python + Ollama AI model application here!

## Submission Requirements

Make sure to include the following details in your README:

### Folder Structure
- **Folder creation**: Please increment the index number as prefix (e.g., `0001`, `0002`, `0003`) and include the project name
- Example: `0001_My-Awesome-Project/`

### Project Information
- **Name** (and Team Name)
- **Project Name**
- **Project Description**
- **Track** specification

## Getting Started

1. Create a new folder with the appropriate naming convention
2. Add your application code
3. Update this README with your project details
4. Submit your project!

## Happy Hacking! 🚀

---

*This is an example submission template for the OpenxAI Global AI Accelerator.*

