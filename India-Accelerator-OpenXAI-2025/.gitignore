# Dependencies
**/node_modules/
node_modules/

# Next.js build output
**/.next/
.next/

# Production builds
**/build/
build/
**/dist/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*
**/lerna-debug.log*

# Runtime data
**/pids
**/*.pid
**/*.seed
**/*.pid.lock

# Coverage directory used by tools like istanbul
**/coverage/
coverage/

# nyc test coverage
**/.nyc_output
.nyc_output

# Dependency directories
**/jspm_packages/
jspm_packages/

# Optional npm cache directory
**/.npm
.npm

# Optional eslint cache
**/.eslintcache
.eslintcache

# Microbundle cache
**/.rpt2_cache/
**/.rts2_cache_cjs/
**/.rts2_cache_es/
**/.rts2_cache_umd/
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
**/.node_repl_history
.node_repl_history

# Output of 'npm pack'
**/*.tgz
*.tgz

# Yarn Integrity file
**/.yarn-integrity
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
**/.cache
**/.parcel-cache
.cache
.parcel-cache

# Next.js build output
**/.next
.next

# Nuxt.js build / generate output
**/.nuxt
**/dist
.nuxt
dist

# Gatsby files
**/.cache/
**/public
.cache/
public

# Storybook build outputs
**/.out
**/.storybook-out
.out
.storybook-out

# Temporary folders
**/tmp/
**/temp/
tmp/
temp/

# Editor directories and files
**/.vscode/
**/.idea/
**/*.swp
**/*.swo
**/*~
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
**/.DS_Store
**/.DS_Store?
**/._*
**/.Spotlight-V100
**/.Trashes
**/ehthumbs.db
**/Thumbs.db
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Ollama models
**/*.gguf
*.gguf

# Nix
**/result
**/result-*
result
result-* 