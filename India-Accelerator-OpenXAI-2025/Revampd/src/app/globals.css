@import "tailwindcss";

:root {
  --background: #000000;
  --foreground: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  margin: 0;
  padding: 0;
}

/* Custom styles for revampd */
.hero-bg {
  background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)),
              url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><rect fill="%23333" width="1200" height="800"/><g fill="%23666"><rect x="100" y="100" width="200" height="300"/><rect x="350" y="150" width="150" height="250"/><rect x="550" y="120" width="180" height="280"/><rect x="780" y="140" width="160" height="260"/><rect x="980" y="110" width="170" height="290"/></g></svg>');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
